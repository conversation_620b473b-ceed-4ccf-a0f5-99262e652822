<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapGrid Test - Quick Functionality Test</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="../../css/tokens.css">
    <link rel="stylesheet" href="../../css/base.css">
    
    <!-- SnapGrid CSS -->
    <link rel="stylesheet" href="snap-grid.css">
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Theme</button>
    
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">SnapGrid Test</h1>
            <p>Quick functionality test for the SnapGrid component</p>
        </div>
        
        <div class="test-controls">
            <button class="test-button primary" onclick="runBasicTest()">Run Basic Test</button>
            <button class="test-button" onclick="testSorting()">Test Sorting</button>
            <button class="test-button" onclick="testFiltering()">Test Filtering</button>
            <button class="test-button" onclick="testEditing()">Test Editing</button>
            <button class="test-button" onclick="testSelection()">Test Selection</button>
            <button class="test-button" onclick="testExport()">Test Export</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="grid-container">
            <div id="testGrid"></div>
        </div>
        
        <div class="test-results">
            <h3>Test Results</h3>
            <div id="testLog" class="test-log">Ready to run tests...\n</div>
        </div>
    </div>
    
    <!-- SnapGrid JavaScript -->
    <script src="snap-grid.js"></script>
    
    <script>
        let grid = null;
        let testData = [];
        
        // Generate test data
        function generateTestData() {
            const data = [];
            const products = ['iPhone', 'Samsung', 'MacBook', 'iPad', 'AirPods'];
            const categories = ['Electronics', 'Computers', 'Audio'];
            
            for (let i = 1; i <= 50; i++) {
                data.push({
                    id: i,
                    name: `${products[Math.floor(Math.random() * products.length)]} ${i}`,
                    category: categories[Math.floor(Math.random() * categories.length)],
                    price: Math.floor(Math.random() * 2000) + 100,
                    stock: Math.floor(Math.random() * 500),
                    active: Math.random() > 0.3,
                    date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
                });
            }
            
            return data;
        }
        
        // Column definitions
        const testColumns = [
            { field: 'id', headerName: 'ID', width: 80, type: 'number', sortable: true },
            { field: 'name', headerName: 'Product Name', width: 200, sortable: true, filterable: true, editable: true },
            { field: 'category', headerName: 'Category', width: 120, sortable: true, filterable: true },
            { field: 'price', headerName: 'Price', width: 100, type: 'currency', sortable: true, editable: true },
            { field: 'stock', headerName: 'Stock', width: 100, type: 'number', sortable: true, editable: true },
            { field: 'active', headerName: 'Active', width: 100, type: 'boolean', sortable: true },
            { field: 'date', headerName: 'Date', width: 120, type: 'date', sortable: true }
        ];
        
        // Logging function
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('testLog').textContent = 'Log cleared...\n';
        }
        
        // Run basic test
        function runBasicTest() {
            log('Starting basic test...');
            
            try {
                // Generate test data
                testData = generateTestData();
                log(`Generated ${testData.length} test records`);
                
                // Create grid
                const container = document.getElementById('testGrid');
                
                if (grid) {
                    grid.destroy();
                    log('Destroyed existing grid');
                }
                
                const startTime = performance.now();
                
                grid = new SnapGrid(container, {
                    data: testData,
                    columns: testColumns,
                    virtualScrolling: true,
                    sortable: true,
                    filterable: true,
                    resizable: true,
                    selectable: true,
                    editable: true,
                    
                    onRowClick: (rowData, rowIndex) => {
                        log(`Row clicked: ${rowData.name} (index: ${rowIndex})`);
                    },
                    
                    onCellEdit: (field, newValue, oldValue, rowData) => {
                        log(`Cell edited: ${field} changed from "${oldValue}" to "${newValue}"`);
                    },
                    
                    onSort: (field, direction) => {
                        log(`Sort applied: ${field} ${direction || 'cleared'}`);
                    },
                    
                    onFilter: (field, value, type) => {
                        log(`Filter applied: ${field} ${type} "${value}"`);
                    }
                });
                
                const endTime = performance.now();
                log(`Grid created successfully in ${(endTime - startTime).toFixed(2)}ms`);
                log('Basic test completed ✅');
                
            } catch (error) {
                log(`Basic test failed ❌: ${error.message}`);
                console.error('Basic test error:', error);
            }
        }
        
        // Test sorting
        function testSorting() {
            if (!grid) {
                log('No grid available. Run basic test first.');
                return;
            }
            
            log('Testing sorting...');
            
            try {
                // Test ascending sort
                grid.setSort('price', 'asc');
                log('Applied ascending sort on price');
                
                setTimeout(() => {
                    // Test descending sort
                    grid.setSort('name', 'desc');
                    log('Applied descending sort on name');
                    
                    setTimeout(() => {
                        // Clear sort
                        grid.clearSort();
                        log('Cleared all sorting');
                        log('Sorting test completed ✅');
                    }, 1000);
                }, 1000);
                
            } catch (error) {
                log(`Sorting test failed ❌: ${error.message}`);
            }
        }
        
        // Test filtering
        function testFiltering() {
            if (!grid) {
                log('No grid available. Run basic test first.');
                return;
            }
            
            log('Testing filtering...');
            
            try {
                // Apply filter
                grid.setFilter('name', 'iPhone', 'contains');
                log('Applied filter: name contains "iPhone"');
                
                setTimeout(() => {
                    // Clear filter
                    grid.clearFilters();
                    log('Cleared all filters');
                    log('Filtering test completed ✅');
                }, 2000);
                
            } catch (error) {
                log(`Filtering test failed ❌: ${error.message}`);
            }
        }
        
        // Test editing
        function testEditing() {
            if (!grid) {
                log('No grid available. Run basic test first.');
                return;
            }
            
            log('Testing editing...');
            log('Click on any editable cell (name, price, stock) to test editing');
        }
        
        // Test selection
        function testSelection() {
            if (!grid) {
                log('No grid available. Run basic test first.');
                return;
            }
            
            log('Testing selection...');
            log('Click on any row to test selection');
            
            setTimeout(() => {
                const selected = grid.getSelectedData();
                log(`Currently selected rows: ${selected.length}`);
            }, 1000);
        }
        
        // Test export
        function testExport() {
            if (!grid) {
                log('No grid available. Run basic test first.');
                return;
            }
            
            log('Testing export...');
            
            try {
                grid.exportToCsv('snap-grid-test-export.csv');
                log('Export test completed ✅');
            } catch (error) {
                log(`Export test failed ❌: ${error.message}`);
            }
        }
        
        // Toggle theme
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
            
            log(`Theme changed to: ${newTheme}`);
        }
        
        // Initialize theme
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = savedTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            initTheme();
            log('SnapGrid test page loaded. Click "Run Basic Test" to start.');
        });
    </script>
</body>
</html>
